<div align="center">
<a href="https://douyin.wtf/" alt="logo" ><img src="https://raw.githubusercontent.com/Evil0ctal/Douyin_TikTok_Download_API/main/logo/logo192.png" width="120"/></a>
</div>
<h1 align="center">Douyin_TikTok_Download_API(抖音/TikTok API)</h1>

<div align="center">

[English](./README.en.md)\|[Simplified Chinese](./README.md)

🚀 "Douyin_TikTok_Download_API" is a high-performance asynchronous out-of-the-box[Tik Tok](https://www.douyin.com)\|[Tiktok](https://www.tiktok.com)\|[Biliable](https://www.bilibili.com)Data crawling tool, supports API calls, online batch analysis and download.

[![GitHub license](https://img.shields.io/github/license/Evil0ctal/Douyin_TikTok_Download_API?style=flat-square)](LICENSE)[![Release Version](https://img.shields.io/github/v/release/Evil0ctal/Douyin_TikTok_Download_API?style=flat-square)](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/releases/latest)[![GitHub Star](https://img.shields.io/github/stars/Evil0ctal/Douyin_TikTok_Download_API?style=flat-square)](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/stargazers)[![GitHub Fork](https://img.shields.io/github/forks/Evil0ctal/Douyin_TikTok_Download_API?style=flat-square)](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/network/members)[![GitHub issues](https://img.shields.io/github/issues/Evil0ctal/Douyin_TikTok_Download_API?style=flat-square)](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/issues)[![GitHub closed issues](https://img.shields.io/github/issues-closed/Evil0ctal/Douyin_TikTok_Download_API?style=flat-square)](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/issues?q=is%3Aissue+is%3Aclosed)![GitHub Repo size](https://img.shields.io/github/repo-size/Evil0ctal/Douyin_TikTok_Download_API?style=flat-square&color=3cb371)<br>[![PyPI v](https://img.shields.io/pypi/v/douyin-tiktok-scraper?style=flat-square&color=%23a8e6cf)](https://pypi.org/project/douyin-tiktok-scraper/)[![PyPI wheel](https://img.shields.io/pypi/wheel/douyin-tiktok-scraper?style=flat-square&color=%23dcedc1)](https://pypi.org/project/douyin-tiktok-scraper/#files)[![PyPI dm](https://img.shields.io/pypi/dm/douyin-tiktok-scraper?style=flat-square&color=%23ffd3b6)](https://pypi.org/project/douyin-tiktok-scraper/)[![PyPI pyversions](https://img.shields.io/pypi/pyversions/douyin-tiktok-scraper?color=%23ffaaa5&style=flat-square)](https://pypi.org/project/douyin-tiktok-scraper/)<br>[![API status](https://img.shields.io/website?down_color=lightgrey&label=API%20Status&down_message=API%20offline&style=flat-square&up_color=%23dfb9ff&up_message=online&url=https%3A%2F%2Fapi.douyin.wtf%2Fdocs)](https://api.douyin.wtf/docs)[![TikHub-API status](https://img.shields.io/website?down_color=lightgrey&label=TikHub-API%20Status&down_message=API%20offline&style=flat-square&up_color=%23dfb9ff&up_message=online&url=https%3A%2F%2Fapi.tikhub.io%2Fdocs)](https://api.tikhub.io/docs)<br>[![爱发电](https://img.shields.io/badge/爱发电-evil0ctal-blue.svg?style=flat-square&color=ea4aaa&logo=github-sponsors)](https://afdian.net/@evil0ctal)[![Kofi](https://img.shields.io/badge/Kofi-evil0ctal-orange.svg?style=flat-square&logo=kofi)](https://ko-fi.com/evil0ctal)[![Patreon](https://img.shields.io/badge/Patreon-evil0ctal-red.svg?style=flat-square&logo=patreon)](https://www.patreon.com/evil0ctal)

</div>

## Sponsors

These sponsors have paid to place them here,**Doinan_tics_download_api**The project will always be free and open source. If you wish to be a sponsor of this project, please check out my[GitHub Sponsor Page](https://github.com/sponsors/evil0ctal)。

<div align="center">
    <a href="https://www.tikhub.io/" target="_blank">
        <img src="https://tikhub.io/wp-content/uploads/2024/11/Main-Logo.webp" width="100" alt="TikHub.io - Global Social Data & API Marketplace">
    </a>
    <div>
        <h2><b>TikHub.io</b></h2>
        <p>Your Ultimate Social Media Data & API Marketplace</p>
        <p>
            Professional data solutions for Douyin, Xiaohongshu, TikTok, Instagram, YouTube, 
            Twitter, and more.<br>
            Real-time Data | Flexible APIs | Seamless Integration | Competitive Pricing with Discounts
        </p>
        <p>
            <b>Discover TikHub.io Marketplace</b><br>
            Buy and sell custom APIs, services, and social media solutions.<br>
            Join a thriving ecosystem of developers, businesses, and content creators.
        </p>
        <p><em>Trusted by leading global influencer marketing and social media intelligence platforms</em></p>
    </div>
</div>

## 👻 Introduction

> 🚨If you want to use a private server to run this project, please refer to:[Deployment preparations](./README.md#%EF%B8%8F%E9%83%A8%E7%BD%B2%E5%89%8D%E7%9A%84%E5%87%86%E5%A4%87%E5%B7%A5%E4%BD%9C%E8%AF%B7%E4%BB%94%E7%BB%86%E9%98%85%E8%AF%BB),[Docker deployment](./README.md#%E9%83%A8%E7%BD%B2%E6%96%B9%E5%BC%8F%E4%BA%8C-docker),[One-click deployment](./README.md#%E9%83%A8%E7%BD%B2%E6%96%B9%E5%BC%8F%E4%B8%80-linux)

This project is based on[Pydebio](https://github.com/pywebio/PyWebIO)，[Fasting](https://fastapi.tiangolo.com/)，[HTTPX](https://www.python-httpx.org/), fast asynchronous[Tik Tok](https://www.douyin.com/)/[Tiktok](https://www.tiktok.com/)Data crawling tool, and online batch analysis and downloading of watermark-free videos or picture albums through the web, data crawling API, iOS shortcuts without watermark download and other functions. You can deploy or transform this project yourself to achieve more functions, or you can call it directly in your project[scraper.py](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/Stable/scraper.py)Or install an existing one[pip package](https://pypi.org/project/douyin-tiktok-scraper/)As a parsing library, easy to crawl data, etc....

_Some simple application scenarios:_

_Download videos that are prohibited from being downloaded, perform data analysis, and download without watermark on iOS (with[iOS's shortcut command APP](https://apps.apple.com/cn/app/%E5%BF%AB%E6%8D%B7%E6%8C%87%E4%BB%A4/id915249334)In conjunction with this project API, it can realize in-app download or read clipboard download, etc...._

## 🔊 V4 version notes

-   If you are interested in writing this project, please add WeChat`Evil0ctal`Note: Github project reconstruction, everyone can communicate and learn from each other in the group, and do not allow advertisements or illegal things to be made purely friends and technical communication.
-   This project uses`X-Bogus`Algorithm and`A_Bogus`The algorithm requests TikTok and TikTok's Web API.
-   Due to Douyin's risk control, please go to**Get the Douyin website cookies in the browser and replace them in config.yaml.**
-   Please read the document below before asking for an issue, and most solutions to the problem will be included in the document.
-   This project is completely free, but please follow it when using it:[Apache-2.0 license](https://github.com/Evil0ctal/Douyin_TikTok_Download_API?tab=Apache-2.0-1-ov-file#readme)

## 🔖TikHub.io API

[TikHub.io](https://api.tikhub.io/)It is an API platform that provides various public data interfaces including Douyin and TikTok. If you want to support it[Doinan_tics_download_api](https://github.com/Evil0ctal/Douyin_TikTok_Download_API)We strongly recommend that you choose the project development[TikHub.io](https://api.tikhub.io/)。

#### Features:

> 📦 Out of the box

Simplify the usage process and quickly carry out development work using the encapsulated SDK. All API interfaces are designed according to the RESTful architecture and are described and documented using the OpenAPI specification, accompanied by example parameters to ensure that calls are easier.

> 💰 Cost Advantage

There is no preset package limit, no monthly usage threshold, all consumption is billed instantly based on the actual usage, and is billed step by step based on the user's daily request volume. At the same time, you can check in in the user's background through daily check-in, and these free amounts will not expire.

> ⚡️ Quick support

We have a huge Discord community server where administrators and other users will quickly reply to you to help you quickly resolve current issues.

> 🎉 Embrace open source

Some of the source code of TikHub will be open sourced on Github and will sponsor some open source projects.

#### Link:

-   Githubub:[TIKHOB GITUB](https://github.com/TikHubIO)
-   Discord:[Tachub](https://discord.com/invite/aMEAS8Xsvz)
-   Register:[TikHub singnup](https://beta-web.tikhub.io/en-us/users/signup)
-   API Docs:[TickHub API Docs](https://api.tikhub.io/)

## 🖥 Demo site: I am very fragile... Please do not press test (·•᷄ࡇ•᷅ )

> 😾The online download function of the demo site has been turned off, and Douyin's parsing and API services cannot be guaranteed for availability on the Demo site due to cookies.

🍔Web APP:<https://douyin.wtf/>

🍟API Document:<https://douyin.wtf/docs>

🌭tikub APU Docuration:<https://api.tikhub.io/docs>

💾 iOS Shortcut:[Shortcut release](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/discussions/104?sort=top)

📦️Desktop downloader (recommended warehouse):

-   [Johnserf-Seed/Tiktokdownload](https://github.com/Johnserf-Seed/TikTokDownload)
-   [HFrost0/bilix](https://github.com/HFrost0/bilix)
-   [Tairraos/TikDown - \[Updated to be\]](https://github.com/Tairraos/TikDown/)

## ⚗️Technology Stack

-   [/app/web](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/main/app/web)-[Pydebio](https://www.pyweb.io/)
-   [/app/api](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/main/app/api)-[Fasting](https://fastapi.tiangolo.com/)
-   [/crawlers](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/main/crawlers)-[HTTPX](https://www.python-httpx.org/)

> **_/crawlers_**

-   Submit requests to APIs of different platforms and retrieve data, and return dictionary (dict) after processing, supports asynchronousness.

> **_/app/api_**

-   Obtain the request parameters and use`Crawlers`After processing data, the related classes return in JSON form, download videos, and implement fast calls with iOS shortcuts, and support asynchronous.

> **_/app/web_**

-   use`PyWebIO`A simple web program created, process the value entered on the web page and use it`Crawlers`The related class processing interface outputs related data on the web page.

**_Most of the parameters of the above files can be in the corresponding`config.yaml`Make modifications in_**

## 💡Project file structure

    ./Douyin_TikTok_Download_API
    ├─app
    │  ├─api
    │  │  ├─endpoints
    │  │  └─models
    │  ├─download
    │  └─web
    │      └─views
    └─crawlers
      ├─bilibili
      │  └─web  
      ├─douyin
      │  └─web
      ├─hybrid
      ├─tiktok
      │  ├─app
      │  └─web
      └─utils

## ✨Support functions:

-   Batch analysis on the web side (supports Douyin/TikTok hybrid analysis)
-   Download videos or albums online.
-   Production[pip package](https://pypi.org/project/douyin-tiktok-scraper/)方便快速导入你的项目
-   [iOS shortcuts to quickly call API](https://apps.apple.com/cn/app/%E5%BF%AB%E6%8D%B7%E6%8C%87%E4%BB%A4/id915249334)Implement watermark-free videos/pictures in-app download
-   Complete API documentation ([Demo/Demo](https://api.douyin.wtf/docs))
-   Rich API interfaces:
    -   TikTok web version API

        -   [x] Video data analysis
        -   [x] Obtain user's homepage work data
        -   [x] Obtain data on the user's homepage liked works
        -   [x] Obtain data on the user's homepage collection of works
        -   [x] Get user homepage information
        -   [x] Obtain user compiled works data
        -   [x] Obtain user live streaming data
        -   [x] Get live streaming data for the specified user
        -   [x] Get the ranking of gift-giving users in the live broadcast room
        -   [x] Get individual video comment data
        -   [x] Get comments and response data for specified videos
        -   [x] Generate msToken
        -   [x] Generate verification_fp
        -   [x] Generate s_v_web_id
        -   [x] Generate X-Bogus parameters using interface URL
        -   [x] Generate A_Bogus parameters using interface URL
        -   [x] Extract a single user id
        -   [x] Extract list user id
        -   [x] Extract individual works id
        -   [x] Extract list work id
        -   [x] Extract list live broadcast room number
        -   [x] Extract list live broadcast room number
    -   TikTok web version API

        -   [x] Video data analysis
        -   [x] Obtain user's homepage work data
        -   [x] Obtain data on the user's homepage liked works
        -   [x] Get user homepage information
        -   [x] Get the user's homepage fan data
        -   [x] Get user's homepage follow data
        -   [x] 获取用户主页合辑作品数据
        -   [x] Get search data for users' homepage
        -   [x] Get user homepage playlist data
        -   [x] Get individual video comment data
        -   [x] Get comments and response data for specified videos
        -   [x] Generate msToken
        -   [x] Generate ttwid
        -   [x] Generate X-Bogus parameters using interface URL
        -   [x] Extract individual user sec_user_id
        -   [x] Extract list user sec_user_id
        -   [x] Extract individual works id
        -   [x] Extract list work id
        -   [x] Get user unique_id
        -   [x] Get the list unique_id
    -   Bilibili web version API
        -   [x] Get individual video details
        -   [x] Get the video streaming address
        -   [x] Obtain data on video works published by users
        -   [x] Get all user favorites information
        -   [x] Get video data in the specified favorites
        -   [x] Get information about the specified user
        -   [x] Get comprehensive popular video information
        -   [x] Get comments for the specified video
        -   [x] Get a reply to the specified comment under the video
        -   [x] Get the specified user dynamics
        -   [x] Get real-time video barrage
        -   [x] Get information about the specified live broadcast room
        -   [x] Get live video streaming
        -   [x] Get the anchor who is currently broadcasting in the specified partition
        -   [x] Get a list of all live partitions
        -   [x] Obtain video score information through bv number

* * *

## 📦 Call the parsing library (deprecated and needs to be updated):

> 💡PIPI ：<https://pypi.org/project/douyin-tiktok-scraper/>

Install the parsing library:`pip install douyin-tiktok-scraper`

```python
import asyncio
from douyin_tiktok_scraper.scraper import Scraper

api = Scraper()

async def hybrid_parsing(url: str) -> dict:
    # Hybrid parsing(Douyin/TikTok URL)
    result = await api.hybrid_parsing(url)
    print(f"The hybrid parsing result:\n {result}")
    return result

asyncio.run(hybrid_parsing(url=input("Paste Douyin/TikTok/Bilibili share URL here: ")))
```

## 🗺️Supported submission formats:

> 💡 Tip: Includes but is not limited to the following examples. If you encounter link resolution failure, please enable a new one.[issue](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/issues)

-   TikTok Sharing Password (Copy within the APP)

```text
7.43 pda:/ 让你在几秒钟之内记住我  https://v.douyin.com/L5pbfdP/ 复制此链接，打开Dou音搜索，直接观看视频！
```

-   TikTok short URL (copy within the APP)

```text
https://v.douyin.com/L4FJNR3/
```

-   Douyin Normal URL (web version copy)

```text
https://www.douyin.com/video/6914948781100338440
```

-   TikTok Discovery Page URL (APP Copy)

```text
https://www.douyin.com/discover?modal_id=7069543727328398622
```

-   TikTok short URL (copy within the APP)

```text
https://www.tiktok.com/t/ZTR9nDNWq/
```

-   TikTok normal website address (web version copy)

```text
https://www.tiktok.com/@evil0ctal/video/7156033831819037994
```

-   TikTok batch URL (no need to use matching separation)

```text
https://v.douyin.com/L4NpDJ6/
https://www.douyin.com/video/7126745726494821640
2.84 nqe:/ 骑白马的也可以是公主%%百万转场变身https://v.douyin.com/L4FJNR3/ 复制此链接，打开Dou音搜索，直接观看视频！
https://www.tiktok.com/t/ZTR9nkkmL/
https://www.tiktok.com/t/ZTR9nDNWq/
https://www.tiktok.com/@evil0ctal/video/7156033831819037994
```

## 🛰️API Documentation

**_API documentation:_**

local:<http://localhost/docs>

Online:<https://api.douyin.wtf/docs>

**_API Demo:_**

-   Crawl video data (TikTok or Douyin mixed analysis)`https://api.douyin.wtf/api/hybrid/video_data?url=[视频链接/Video URL]&minimal=false`
-   Download video/picture album (TikTok or Douyin mixed analysis)`https://api.douyin.wtf/api/download?url=[视频链接/Video URL]&prefix=true&with_watermark=false`

**_For more demonstrations, please check the document content..._**

## ⚠️Preparation before deployment (please read carefully):

-   You need to solve the risk control problem of crawler cookies by yourself, otherwise the interface may be unavailable. After modifying the configuration file, you need to restart the service before it takes effect. It is best to use cookies from the account you have logged in.
    -   Douyin web cookies (acquire and replace cookies in the following configuration files):
    -   <https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/30e56e5a7f97f87d60b1045befb1f6db147f8590/crawlers/douyin/web/config.yaml#L7>
    -   TikTok web cookies (acquire and replace cookies in the following configuration files):
    -   <https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/30e56e5a7f97f87d60b1045befb1f6db147f8590/crawlers/tiktok/web/config.yaml#L6>
-   I turned off the online download function of the demonstration site. Someone downloaded a huge video and it crashed directly on my server. You can right-click to save the video on the web parsing result page...
-   The cookies on the demo site are my own and are not guaranteed to be valid for a long time. They only serve as a demonstration. If you deploy it yourself, please get the cookies yourself.
-   HTTP 403 error will occur if you need to access the video link returned by TikTok Web API. Please use the API in this project`/api/download`The interface downloads TikTok videos. This interface has been manually closed in the demonstration site, and you need to deploy this project yourself.
-   There is one here**Video tutorial**You can refer to:**_<https://www.bilibili.com/video/BV1vE421j7NR/>_**

## 💻Deployment (Method 1 Linux)

> 💡Tip: It is best to deploy this project to a server in the United States, otherwise strange bugs may occur.

Recommended to use[DigitalOcean](https://www.digitalocean.com/)server, because it can be free.

Sign up with my invitation link and you can get a credit of $200, and I can get a reward of $25 when you spend $25 on it.

My invitation link:

<https://m.do.co/c/9f72a27dec35>

> Use scripts to deploy this project in one click

-   This project provides one-click deployment scripts to quickly deploy the project on the server.
-   The script was tested on Ubuntu 20.04 LTS, and other systems may have problems. If there are any problems, please solve them yourself.
-   Download using wget command[install.sh](https://raw.githubusercontent.com/Evil0ctal/Douyin_TikTok_Download_API/main/bash/install.sh)Go to the server and run


    wget -O install.sh https://raw.githubusercontent.com/Evil0ctal/Douyin_TikTok_Download_API/main/bash/install.sh && sudo bash install.sh

> Turn on/stop service

-   Use the following command to control the operation or stop of the service:
    -   `sudo systemctl start Douyin_TikTok_Download_API.service`
    -   `sudo systemctl stop Douyin_TikTok_Download_API.service`

> Turn on/off automatically

-   Use the following command to set the service to automatically run on or cancel the automatic run on:
    -   `sudo systemctl enable Douyin_TikTok_Download_API.service`
    -   `sudo systemctl disable Douyin_TikTok_Download_API.service`

> Update the project

-   When the project is updated, make sure that the update script is executed in the virtual environment and update all dependencies. Enter the project bash directory and run update.sh:
-   `cd /www/wwwroot/Douyin_TikTok_Download_API/bash && sudo bash update.sh`

## 💽Deployment (Method 2 Docker)

> 💡 Tip: Docker deployment is the easiest way to deploy, suitable for users who are not familiar with Linux. This method is suitable for ensuring environmental consistency, isolation and quick settings.
> Please use a server that can access Douyin or TikTok normally, otherwise strange bugs may occur.

### Preparation

Before you begin, make sure your system has Docker installed. If Docker is not installed, you can[Docker official website](https://www.docker.com/products/docker-desktop/)Download and install.

### Step 1: Pull the Docker image

First, pull the latest Douyin_TikTok_Download_API image from Docker Hub.

```bash
docker pull evil0ctal/douyin_tiktok_download_api:latest
```

If necessary, you can replace it`latest`Tags for the specific version you need to deploy.

### Step 2: Run the Docker container

After pulling the image, you can start a container from this image. The following are the commands to run the container, including the basic configuration:

```bash
docker run -d --name douyin_tiktok_api -p 80:80 evil0ctal/douyin_tiktok_download_api
```

Each part of this command works as follows:

-   `-d`: Run containers in the background (separated mode).
-   `--name douyin_tiktok_api `: Name the container`douyin_tiktok_api `。
-   `-p 80:80`: Map port 80 on the host to port 80 of the container. Adjust the port number according to your configuration or port availability.
-   `evil0ctal/douyin_tiktok_download_api`: The name of the Docker image to be used.

### Step 3: Verify that the container is running

Use the following command to check if your container is running:

```bash
docker ps
```

这将列出所有活动容器。查找`douyin_tiktok_api `to confirm its normal operation.

### Step 4: Access the application

After the container runs, you should be able to pass`http://localhost`Or the API client access Douyin_TikTok_Download_API. If you have a different port configured or accessed from a remote location, adjust the URL.

### Optional: Custom Docker commands

For more advanced deployments, you may want to customize Docker commands, including environment variables, volume mounts for persistent data, or other Docker parameters. Here is an example:

```bash
docker run -d --name douyin_tiktok_api -p 80:80 \
  -v /path/to/your/data:/data \
  -e MY_ENV_VAR=my_value \
  evil0ctal/douyin_tiktok_download_api
```

-   `-v /path/to/your/data:/data`: Turn on the host`/path/to/your/data`The directory mounted to the container`/data`Directory, used to persist or share data.
-   `-e MY_ENV_VAR=my_value`: Set environment variables in the container`MY_ENV_VAR`, its value is`my_value`。

### Configuration file modification

Most of the configurations of the project can be found in the following directories`config.yaml`Modify the file:

-   `/crawlers/douyin/web/config.yaml`
-   `/crawlers/tiktok/web/config.yaml`
-   `/crawlers/tiktok/app/config.yaml`

### Step 5: Stop and remove the container

When you need to stop and remove the container, use the following command:

```bash
# Stop
docker stop douyin_tiktok_api 

# Remove
docker rm douyin_tiktok_api
```

## 📸Screenshot

**_API speed test (compare the official API)_**

<details><summary>🔎点击展开截图</summary>

TikTok official API:![](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/main/Screenshots/benchmarks/Douyin_API.png?raw=true)

This project API:![](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/main/Screenshots/benchmarks/Douyin_API_Douyin_wtf.png?raw=true)

TikTok official API:![](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/main/Screenshots/benchmarks/TikTok_API.png?raw=true)

This project API:![](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/main/Screenshots/benchmarks/TikTok_API_Douyin_wtf.png?raw=true)

</details>
<hr>

**_Project interface_**

<details><summary>🔎点击展开截图</summary>

Web main interface:

![](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/main/Screenshots/v3_screenshots/Home.png?raw=true)

Web main interface:

![](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/main/Screenshots/v3_screenshots/Home_en.png?raw=true)

</details>
<hr>

## 📜 Star History

[![Star History Chart](https://api.star-history.com/svg?repos=Evil0ctal/Douyin_TikTok_Download_API&type=Timeline)](https://star-history.com/#Evil0ctal/Douyin_TikTok_Download_API&Timeline)

[Apache-2.0 license](https://github.com/Evil0ctal/Douyin_TikTok_Download_API/blob/Stable/LICENSE)

> Start: 2021/11/06

> Githubub:[@Evil0ctal](https://github.com/Evil0ctal)
